package de.adesso.fischereiregister.registerservice.statistics.fees_statistics;

import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.view.fees_statistics.persistence.FeesStatisticsView;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class FeesStatisticsTransformationServiceImplTest {

    private FeesStatisticsTransformationServiceImpl service;

    @BeforeEach
    void setUp() {
        service = new FeesStatisticsTransformationServiceImpl();
    }

    @Test
    @DisplayName("Should transform fees statistics views to domain model correctly")
    void shouldTransformFeesStatisticsViewsToDomainModel() {
        // given
        FeesStatisticsView view1 = createView(2023, SubmissionType.ONLINE, "SH", "Office1", 5, 100.0);
        FeesStatisticsView view2 = createView(2023, SubmissionType.ANALOG, "SH", "Office1", 3, 60.0);
        FeesStatisticsView view3 = createView(2023, SubmissionType.ONLINE, "SH", "Office2", 2, 40.0);
        FeesStatisticsView view4 = createView(2024, SubmissionType.ONLINE, "SH", "Office1", 7, 140.0);
        FeesStatisticsView view5 = createView(2024, SubmissionType.ANALOG, "SH", "Office1", 4, 80.0);

        List<FeesStatisticsView> views = List.of(view1, view2, view3, view4, view5);
        List<Integer> yearsToQuery = List.of(2023, 2024);

        // when
        List<FeesStatistics> result = service.transformToFeesStatistics(views, yearsToQuery);

        // then
        assertThat(result).hasSize(2); // Two years: 2023 and 2024

        // Check 2023 statistics
        FeesStatistics stats2023 = result.stream()
                .filter(s -> s.year() == 2023)
                .findFirst()
                .orElseThrow();

        assertThat(stats2023.year()).isEqualTo(2023);
        assertThat(stats2023.data()).hasSize(2); // Two submission types: ONLINE and ANALOG

        // Check ONLINE entries for 2023 (should be sum of view1 and view3)
        FeesStatisticsDataEntry onlineEntry2023 = stats2023.data().stream()
                .filter(d -> d.submissionType() == SubmissionType.ONLINE)
                .findFirst()
                .orElseThrow();
        assertThat(onlineEntry2023.count()).isEqualTo(7); // 5 + 2
        assertThat(onlineEntry2023.revenue()).isEqualTo(140.0); // 100.0 + 40.0

        // Check ANALOG entries for 2023
        FeesStatisticsDataEntry analogEntry2023 = stats2023.data().stream()
                .filter(d -> d.submissionType() == SubmissionType.ANALOG)
                .findFirst()
                .orElseThrow();
        assertThat(analogEntry2023.count()).isEqualTo(3);
        assertThat(analogEntry2023.revenue()).isEqualTo(60.0);

        // Check 2024 statistics
        FeesStatistics stats2024 = result.stream()
                .filter(s -> s.year() == 2024)
                .findFirst()
                .orElseThrow();

        assertThat(stats2024.year()).isEqualTo(2024);
        assertThat(stats2024.data()).hasSize(2); // Two submission types: ONLINE and ANALOG

        // Check ONLINE entries for 2024
        FeesStatisticsDataEntry onlineEntry2024 = stats2024.data().stream()
                .filter(d -> d.submissionType() == SubmissionType.ONLINE)
                .findFirst()
                .orElseThrow();
        assertThat(onlineEntry2024.count()).isEqualTo(7);
        assertThat(onlineEntry2024.revenue()).isEqualTo(140.0);

        // Check ANALOG entries for 2024
        FeesStatisticsDataEntry analogEntry2024 = stats2024.data().stream()
                .filter(d -> d.submissionType() == SubmissionType.ANALOG)
                .findFirst()
                .orElseThrow();
        assertThat(analogEntry2024.count()).isEqualTo(4);
        assertThat(analogEntry2024.revenue()).isEqualTo(80.0);
    }

    @Test
    @DisplayName("Should fill missing years with zero values")
    void shouldFillMissingYearsWithZeroValues() {
        // given
        FeesStatisticsView view1 = createView(2023, SubmissionType.ONLINE, "SH", "Office1", 5, 100.0);
        // Note: No data for 2024

        List<FeesStatisticsView> views = List.of(view1);
        List<Integer> yearsToQuery = List.of(2023, 2024);

        // when
        List<FeesStatistics> result = service.transformToFeesStatistics(views, yearsToQuery);

        // then
        assertThat(result).hasSize(2);

        // Check 2023 has data
        FeesStatistics stats2023 = result.stream()
                .filter(s -> s.year() == 2023)
                .findFirst()
                .orElseThrow();
        assertThat(stats2023.data()).hasSize(2); // ONLINE and ANALOG
        assertThat(stats2023.data().stream()
                .filter(d -> d.submissionType() == SubmissionType.ONLINE)
                .findFirst()
                .orElseThrow()
                .count()).isEqualTo(5);

        // Check 2024 has zero values
        FeesStatistics stats2024 = result.stream()
                .filter(s -> s.year() == 2024)
                .findFirst()
                .orElseThrow();
        assertThat(stats2024.data()).hasSize(2); // ONLINE and ANALOG with zero values
        assertThat(stats2024.data().stream()
                .filter(d -> d.submissionType() == SubmissionType.ONLINE)
                .findFirst()
                .orElseThrow()
                .count()).isEqualTo(0);
        assertThat(stats2024.data().stream()
                .filter(d -> d.submissionType() == SubmissionType.ANALOG)
                .findFirst()
                .orElseThrow()
                .count()).isEqualTo(0);
    }

    @Test
    @DisplayName("Should handle empty list of views")
    void shouldHandleEmptyListOfViews() {
        // given
        List<FeesStatisticsView> views = List.of();
        List<Integer> yearsToQuery = List.of(2023, 2024);

        // when
        List<FeesStatistics> result = service.transformToFeesStatistics(views, yearsToQuery);

        // then
        assertThat(result).hasSize(2); // Two years requested

        // Each year should have zero data for both submission types
        result.forEach(stats -> {
            assertThat(stats.data()).hasSize(2); // ONLINE and ANALOG
            assertThat(stats.data().stream()
                    .allMatch(d -> d.count() == 0 && d.revenue() == 0.0)).isTrue();
        });
    }

    @Test
    @DisplayName("Should handle empty years list")
    void shouldHandleEmptyYearsList() {
        // given
        FeesStatisticsView view1 = createView(2023, SubmissionType.ONLINE, "SH", "Office1", 5, 100.0);
        List<FeesStatisticsView> views = List.of(view1);
        List<Integer> yearsToQuery = List.of();

        // when
        List<FeesStatistics> result = service.transformToFeesStatistics(views, yearsToQuery);

        // then
        assertThat(result).isEmpty();
    }

    @Test
    @DisplayName("Should handle single entry")
    void shouldHandleSingleEntry() {
        // given
        FeesStatisticsView view = createView(2023, SubmissionType.ONLINE, "SH", "Office1", 5, 100.0);
        List<FeesStatisticsView> views = List.of(view);
        List<Integer> yearsToQuery = List.of(2023);

        // when
        List<FeesStatistics> result = service.transformToFeesStatistics(views, yearsToQuery);

        // then
        assertThat(result).hasSize(1);
        assertThat(result.get(0).year()).isEqualTo(2023);
        assertThat(result.get(0).data()).hasSize(2); // ONLINE and ANALOG
        
        FeesStatisticsDataEntry onlineEntry = result.get(0).data().stream()
                .filter(d -> d.submissionType() == SubmissionType.ONLINE)
                .findFirst()
                .orElseThrow();
        assertThat(onlineEntry.count()).isEqualTo(5);
        assertThat(onlineEntry.revenue()).isEqualTo(100.0);

        FeesStatisticsDataEntry analogEntry = result.get(0).data().stream()
                .filter(d -> d.submissionType() == SubmissionType.ANALOG)
                .findFirst()
                .orElseThrow();
        assertThat(analogEntry.count()).isEqualTo(0);
        assertThat(analogEntry.revenue()).isEqualTo(0.0);
    }

    @Test
    @DisplayName("Should sort results by year in descending order")
    void shouldSortResultsByYearDescending() {
        // given
        FeesStatisticsView view1 = createView(2022, SubmissionType.ONLINE, "SH", "Office1", 1, 20.0);
        FeesStatisticsView view2 = createView(2024, SubmissionType.ONLINE, "SH", "Office1", 3, 60.0);
        FeesStatisticsView view3 = createView(2023, SubmissionType.ONLINE, "SH", "Office1", 2, 40.0);

        List<FeesStatisticsView> views = List.of(view1, view2, view3);
        List<Integer> yearsToQuery = List.of(2022, 2023, 2024);

        // when
        List<FeesStatistics> result = service.transformToFeesStatistics(views, yearsToQuery);

        // then
        assertThat(result).hasSize(3);
        assertThat(result.get(0).year()).isEqualTo(2024); // First should be highest year
        assertThat(result.get(1).year()).isEqualTo(2023);
        assertThat(result.get(2).year()).isEqualTo(2022); // Last should be lowest year
    }

    private FeesStatisticsView createView(int year, SubmissionType submissionType, String federalState, String office, int count, double revenue) {
        FeesStatisticsView view = new FeesStatisticsView();
        view.setYear(year);
        view.setSource(submissionType);
        view.setFederalState(federalState);
        view.setOffice(office);
        view.setCount(count);
        view.setRevenue(revenue);
        return view;
    }
}

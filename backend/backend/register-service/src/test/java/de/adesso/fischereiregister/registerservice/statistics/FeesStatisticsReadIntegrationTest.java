package de.adesso.fischereiregister.registerservice.statistics;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.core.events.FeePayedEvent;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.consent.JurisdictionConsentInfo;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import de.adesso.fischereiregister.view.fees_statistics.eventhandler.FeesStatisticsEventHandler;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.UUID;

import static org.hamcrest.Matchers.greaterThanOrEqualTo;
import static org.hamcrest.Matchers.hasItem;
import static org.hamcrest.Matchers.hasSize;
import static org.mockito.Mockito.mock;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class FeesStatisticsReadIntegrationTest {

    // greaterThanOrEqualTo is used for the assertions as the data is still contaminated by the file test-data.xml, please correct when the file gets deleted

    private static final String PARAM_YEAR = "year";
    private static final String PARAM_FEDERAL_STATE = "federalState";
    private static final String PARAM_OFFICE = "office";

    private static final String TEST_FEDERAL_STATE_SH = "SH";
    private static final String TEST_FEDERAL_STATE_HH = "HH";
    private static final String TEST_OFFICE = "Ministerium für Energiewende, Landwirtschaft, Umwelt, Natur und Digitalisierung";

    private static final int CURRENT_YEAR = LocalDateTime.now().getYear();
    private static final int PREVIOUS_YEAR = CURRENT_YEAR - 1;

    private static final double FEE_AMOUNT_SH = 25.0;
    private static final double FEE_AMOUNT_HH = 30.0;
    private static final double FEE_AMOUNT_VACATION = 15.0;
    private static final double FEE_AMOUNT_DIGITIZED = 20.0;

    @Autowired
    private FeesStatisticsEventHandler eventHandler;

    @Autowired
    private MockMvc mvc;

    @BeforeAll
    void setUp() {
        // Create events and call handler directly
        Instant currentYearInstant = LocalDateTime.of(CURRENT_YEAR, 1, 1, 0, 0, 0).toInstant(ZoneOffset.UTC);
        Instant previousYearInstant = LocalDateTime.of(PREVIOUS_YEAR, 1, 1, 0, 0, 0).toInstant(ZoneOffset.UTC);

        // Create FeePayedEvent for SH with ANALOG submission
        FeePayedEvent feePayedEventSH1 = createFeePayedEvent(SubmissionType.ANALOG, TEST_FEDERAL_STATE_SH, FEE_AMOUNT_SH);

        // Create FeePayedEvent for SH with ONLINE submission
        FeePayedEvent feePayedEventSH2 = createFeePayedEvent(SubmissionType.ONLINE, TEST_FEDERAL_STATE_SH, FEE_AMOUNT_SH);

        // Create FeePayedEvent for HH with ANALOG submission
        FeePayedEvent feePayedEventHH = createFeePayedEvent(SubmissionType.ANALOG, TEST_FEDERAL_STATE_HH, FEE_AMOUNT_HH);

        // Create FeePayedEvent for vacation license with ONLINE submission
        FeePayedEvent feePayedEventVacation = createFeePayedEvent(SubmissionType.ONLINE, TEST_FEDERAL_STATE_SH, FEE_AMOUNT_VACATION);

        // Create FeePayedEvent for digitized license with ANALOG submission
        FeePayedEvent feePayedEventDigitized = createFeePayedEvent(SubmissionType.ANALOG, TEST_FEDERAL_STATE_SH, FEE_AMOUNT_DIGITIZED);

        // Create FeePayedEvent for previous year with ONLINE submission
        FeePayedEvent feePayedEventPreviousYear = createFeePayedEvent(SubmissionType.ONLINE, TEST_FEDERAL_STATE_SH, FEE_AMOUNT_SH);

        // Call event handler directly to update statistics
        eventHandler.on(feePayedEventSH1);
        eventHandler.on(feePayedEventSH2);
        eventHandler.on(feePayedEventHH);
        eventHandler.on(feePayedEventVacation);
        eventHandler.on(feePayedEventDigitized);
        eventHandler.on(feePayedEventPreviousYear);
    }

    @Test
    @DisplayName("""
            GET /api/statistics/fees
            Verify that the fees statistics endpoint can be reached and delivers the proper information.
            """)
    void callGetFeesStatisticsSuccessful() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/fees")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .param(PARAM_FEDERAL_STATE, TEST_FEDERAL_STATE_SH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // Year matches parameter
                .andExpect(jsonPath("$[0].data").isArray()) // Data is an array
                .andExpect(jsonPath("$[0].data", hasSize(greaterThanOrEqualTo(2)))) // At least ONLINE and ANALOG
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ANALOG')].count").value(hasItem(greaterThanOrEqualTo(2)))) // Count for ANALOG submissions (1 SH + 1 digitized)
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ONLINE')].count").value(hasItem(greaterThanOrEqualTo(2)))) // Count for ONLINE submissions (1 SH + 1 vacation)
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ANALOG')].revenue").value(hasItem(greaterThanOrEqualTo(FEE_AMOUNT_SH + FEE_AMOUNT_DIGITIZED)))) // Revenue for ANALOG submissions
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ONLINE')].revenue").value(hasItem(greaterThanOrEqualTo(FEE_AMOUNT_SH + FEE_AMOUNT_VACATION)))); // Revenue for ONLINE submissions
    }

    @Test
    @DisplayName("""
            GET /api/statistics/fees with year parameter
            Verify that the fees statistics endpoint correctly filters by year.
            """)
    void callGetFeesStatisticsWithYearFilter() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/fees")
                        .param(PARAM_YEAR, String.valueOf(PREVIOUS_YEAR))
                        .param(PARAM_FEDERAL_STATE, TEST_FEDERAL_STATE_SH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$[0].year").value(PREVIOUS_YEAR)) // Year matches parameter
                .andExpect(jsonPath("$[0].data").isArray()) // Data is an array
                .andExpect(jsonPath("$[0].data", hasSize(greaterThanOrEqualTo(2)))) // At least ONLINE and ANALOG
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ONLINE')].count").value(hasItem(greaterThanOrEqualTo(1)))) // Count for ONLINE submissions in previous year
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ONLINE')].revenue").value(hasItem(greaterThanOrEqualTo(FEE_AMOUNT_SH)))); // Revenue for ONLINE submissions in previous year
    }

    @Test
    @DisplayName("""
            GET /api/statistics/fees without federalState parameter
            Verify that the fees statistics endpoint returns data for all federal states when no federalState parameter is provided.
            """)
    void callGetFeesStatisticsWithoutFederalStateSuccessful() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/fees")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                // Data from all federal states for the current year
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // Year matches parameter
                .andExpect(jsonPath("$[0].data").isArray()) // Data is an array
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ANALOG')].count").value(hasItem(greaterThanOrEqualTo(3)))) // Count for ANALOG submissions (2 SH + 1 HH)
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ONLINE')].count").value(hasItem(greaterThanOrEqualTo(2)))) // Count for ONLINE submissions (2 SH)
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ANALOG')].revenue").value(hasItem(greaterThanOrEqualTo(FEE_AMOUNT_SH + FEE_AMOUNT_DIGITIZED + FEE_AMOUNT_HH)))) // Revenue for ANALOG submissions
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ONLINE')].revenue").value(hasItem(greaterThanOrEqualTo(FEE_AMOUNT_SH + FEE_AMOUNT_VACATION)))); // Revenue for ONLINE submissions
    }

    @Test
    @DisplayName("""
            GET /api/statistics/fees with office parameter
            Verify that the fees statistics endpoint correctly filters by office.
            """)
    void callGetFeesStatisticsWithOfficeSuccessful() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/fees")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .param(PARAM_OFFICE, TEST_OFFICE)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // Year matches parameter
                .andExpect(jsonPath("$[0].data").isArray()) // Data is an array
                .andExpect(jsonPath("$[0].data", hasSize(greaterThanOrEqualTo(2)))); // At least ONLINE and ANALOG
    }

    @Test
    @DisplayName("""
            GET /api/statistics/fees without parameters
            Verify that the fees statistics endpoint returns all available data when no parameters are provided.
            """)
    void callGetFeesStatisticsWithoutParametersSuccessful() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/fees")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                // Data from all years and federal states
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // First result year (current year)
                .andExpect(jsonPath("$[1].year").value(PREVIOUS_YEAR)) // Second result year (previous year)
                .andExpect(jsonPath("$[*].data").exists()) // Data objects exist
                // Current year assertions
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ANALOG')].count").value(hasItem(greaterThanOrEqualTo(3)))) // Count for ANALOG submissions in current year
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ONLINE')].count").value(hasItem(greaterThanOrEqualTo(2)))) // Count for ONLINE submissions in current year
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ANALOG')].revenue").value(hasItem(greaterThanOrEqualTo(FEE_AMOUNT_SH + FEE_AMOUNT_DIGITIZED + FEE_AMOUNT_HH)))) // Revenue for ANALOG submissions in current year
                // Previous year assertions
                .andExpect(jsonPath("$[1].data[?(@.submissionType == 'ONLINE')].count").value(hasItem(greaterThanOrEqualTo(1)))); // Count for ONLINE submissions in previous year
    }

    @Test
    @DisplayName("""
            GET /api/statistics/fees with non-existent year parameter
            Verify that the fees statistics endpoint returns an empty array for a non-existent year.
            """)
    void callGetFeesStatisticsWithNonExistentYear() throws Exception {
        String nonExistentYear = String.valueOf(2000); // Use a year with no data

        mvc.perform(MockMvcRequestBuilders.get("/statistics/fees")
                        .param(PARAM_YEAR, nonExistentYear)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray()) // Response is an array
                .andExpect(jsonPath("$").isEmpty()); // Empty array for non-existent year
    }

    private FeePayedEvent createFeePayedEvent(SubmissionType submissionType, String federalState, double amount) {
        Person person = new Person(
                UUID.randomUUID(),
                "John",
                "Doe",
                "<EMAIL>",
                "123 Main St",
                "12345",
                "Anytown",
                "1234567890",
                mock(JurisdictionConsentInfo.class)
        );

        Jurisdiction jurisdiction = new Jurisdiction(
                federalState,
                TEST_OFFICE,
                "Test Region"
        );

        Fee fee = new Fee(
                UUID.randomUUID(),
                amount,
                "EUR"
        );

        return new FeePayedEvent(
                UUID.randomUUID(),
                person,
                jurisdiction,
                fee,
                submissionType,
                Instant.now()
        );
    }
}

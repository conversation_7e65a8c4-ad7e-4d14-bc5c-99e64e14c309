package de.adesso.fischereiregister.registerservice.statistics.taxes_statistics;

import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.view.taxes_statistics.persistence.TaxesStatisticsView;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class TaxesStatisticsTransformationServiceImplTest {

    private TaxesStatisticsTransformationServiceImpl transformationService;

    @BeforeEach
    void setUp() {
        transformationService = new TaxesStatisticsTransformationServiceImpl();
    }

    @Test
    @DisplayName("Should correctly group and sum counts by year, source, and duration")
    void transformToTaxesStatistics_ShouldGroupAndSumCountsCorrectly() {
        // given
        TaxesStatisticsView entry1 = createStatisticsView(2023, SubmissionType.ONLINE, 1, "BE", "Senatsverwaltung für Umwelt", 5, 100.0);
        TaxesStatisticsView entry2 = createStatisticsView(2023, SubmissionType.ANALOG, 1, "BE", "Senatsverwaltung für Umwelt", 3, 60.0);
        TaxesStatisticsView entry3 = createStatisticsView(2023, SubmissionType.ONLINE, 3, "BE", "Senatsverwaltung für Umwelt", 2, 120.0);
        TaxesStatisticsView entry4 = createStatisticsView(2023, SubmissionType.ONLINE, 1, "NW", "Regierungspräsidium Düsseldorf", 4, 80.0);
        TaxesStatisticsView entry5 = createStatisticsView(2024, SubmissionType.ONLINE, 1, "NW", "Regierungspräsidium Freiburg", 11, 220.0);
        TaxesStatisticsView entry6 = createStatisticsView(2024, SubmissionType.ANALOG, 1, "HE", "Regierungspräsidium Gießen", 10, 200.0);
        TaxesStatisticsView entry7 = createStatisticsView(2023, SubmissionType.ANALOG, 5, "SA", "Sächsisches Staatsministerium", 1, 50.0);
        TaxesStatisticsView entry8 = createStatisticsView(2024, SubmissionType.ONLINE, 5, "HE", "Regierungspräsidium Gießen", 12, 600.0);
        TaxesStatisticsView entry9 = createStatisticsView(2024, SubmissionType.ANALOG, 3, "NW", "Regierungspräsidium Freiburg", 2, 100.0);

        List<TaxesStatisticsView> views = List.of(entry1, entry2, entry3, entry4, entry5, entry6, entry7, entry8, entry9);
        List<Integer> yearsToQuery = List.of(2023, 2024);

        // when
        List<TaxesStatistics> result = transformationService.transformToTaxesStatistics(views, yearsToQuery);

        // then
        assertThat(result).hasSize(2);
        
        // Check 2023 statistics
        TaxesStatistics stats2023 = result.stream()
                .filter(s -> s.year() == 2023)
                .findFirst()
                .orElseThrow();
        assertThat(stats2023.year()).isEqualTo(2023);
        assertThat(stats2023.data()).hasSizeGreaterThanOrEqualTo(4);

        // Check specific combinations for 2023
        assertThat(getTotalCount(stats2023, SubmissionType.ONLINE, 1)).isEqualTo(9); // 5 + 4
        assertThat(getTotalRevenue(stats2023, SubmissionType.ONLINE, 1)).isEqualTo(180.0); // 100.0 + 80.0
        assertThat(getTotalCount(stats2023, SubmissionType.ONLINE, 3)).isEqualTo(2);
        assertThat(getTotalRevenue(stats2023, SubmissionType.ONLINE, 3)).isEqualTo(120.0);
        assertThat(getTotalCount(stats2023, SubmissionType.ANALOG, 1)).isEqualTo(3);
        assertThat(getTotalRevenue(stats2023, SubmissionType.ANALOG, 1)).isEqualTo(60.0);
        assertThat(getTotalCount(stats2023, SubmissionType.ANALOG, 5)).isEqualTo(1);
        assertThat(getTotalRevenue(stats2023, SubmissionType.ANALOG, 5)).isEqualTo(50.0);

        // Check 2024 statistics
        TaxesStatistics stats2024 = result.stream()
                .filter(s -> s.year() == 2024)
                .findFirst()
                .orElseThrow();
        assertThat(stats2024.year()).isEqualTo(2024);
        assertThat(stats2024.data()).hasSizeGreaterThanOrEqualTo(4);

        // Check specific combinations for 2024
        assertThat(getTotalCount(stats2024, SubmissionType.ONLINE, 1)).isEqualTo(11);
        assertThat(getTotalRevenue(stats2024, SubmissionType.ONLINE, 1)).isEqualTo(220.0);
        assertThat(getTotalCount(stats2024, SubmissionType.ONLINE, 5)).isEqualTo(12);
        assertThat(getTotalRevenue(stats2024, SubmissionType.ONLINE, 5)).isEqualTo(600.0);
        assertThat(getTotalCount(stats2024, SubmissionType.ANALOG, 1)).isEqualTo(10);
        assertThat(getTotalRevenue(stats2024, SubmissionType.ANALOG, 1)).isEqualTo(200.0);
        assertThat(getTotalCount(stats2024, SubmissionType.ANALOG, 3)).isEqualTo(2);
        assertThat(getTotalRevenue(stats2024, SubmissionType.ANALOG, 3)).isEqualTo(100.0);
    }

    @Test
    @DisplayName("Should handle empty list")
    void transformToTaxesStatistics_ShouldHandleEmptyList() {
        // given
        List<TaxesStatisticsView> views = List.of();
        List<Integer> yearsToQuery = List.of(2023, 2024);

        // when
        List<TaxesStatistics> result = transformationService.transformToTaxesStatistics(views, yearsToQuery);

        // then
        assertThat(result).hasSize(2); // Should return entries for all requested years with zero values
        
        result.forEach(stats -> {
            assertThat(stats.data()).hasSize(2); // ONLINE and ANALOG with duration 1
            assertThat(getTotalCount(stats, SubmissionType.ONLINE, 1)).isEqualTo(0);
            assertThat(getTotalRevenue(stats, SubmissionType.ONLINE, 1)).isEqualTo(0.0);
            assertThat(getTotalCount(stats, SubmissionType.ANALOG, 1)).isEqualTo(0);
            assertThat(getTotalRevenue(stats, SubmissionType.ANALOG, 1)).isEqualTo(0.0);
        });
    }

    @Test
    @DisplayName("Should handle single entry")
    void transformToTaxesStatistics_ShouldHandleSingleEntry() {
        // given
        TaxesStatisticsView entry = createStatisticsView(2023, SubmissionType.ONLINE, 1, "BE", "Senatsverwaltung für Umwelt", 5, 100.0);
        List<TaxesStatisticsView> views = List.of(entry);
        List<Integer> yearsToQuery = List.of(2023);

        // when
        List<TaxesStatistics> result = transformationService.transformToTaxesStatistics(views, yearsToQuery);

        // then
        assertThat(result).hasSize(1);
        assertThat(result.get(0).year()).isEqualTo(2023);
        assertThat(result.get(0).data()).hasSize(2); // ONLINE and ANALOG with duration 1
        assertThat(getTotalCount(result.get(0), SubmissionType.ONLINE, 1)).isEqualTo(5);
        assertThat(getTotalRevenue(result.get(0), SubmissionType.ONLINE, 1)).isEqualTo(100.0);
        assertThat(getTotalCount(result.get(0), SubmissionType.ANALOG, 1)).isEqualTo(0);
        assertThat(getTotalRevenue(result.get(0), SubmissionType.ANALOG, 1)).isEqualTo(0.0);
    }

    @Test
    @DisplayName("Should handle empty years list")
    void transformToTaxesStatistics_ShouldHandleEmptyYearsList() {
        // given
        TaxesStatisticsView entry = createStatisticsView(2023, SubmissionType.ONLINE, 1, "BE", "Senatsverwaltung für Umwelt", 5, 100.0);
        List<TaxesStatisticsView> views = List.of(entry);
        List<Integer> yearsToQuery = List.of();

        // when
        List<TaxesStatistics> result = transformationService.transformToTaxesStatistics(views, yearsToQuery);

        // then
        assertThat(result).isEmpty();
    }

    @Test
    @DisplayName("Should fill missing years with zero values")
    void transformToTaxesStatistics_ShouldFillMissingYearsWithZeroValues() {
        // given
        TaxesStatisticsView entry = createStatisticsView(2023, SubmissionType.ONLINE, 1, "BE", "Senatsverwaltung für Umwelt", 5, 100.0);
        // Note: No data for 2024
        List<TaxesStatisticsView> views = List.of(entry);
        List<Integer> yearsToQuery = List.of(2023, 2024);

        // when
        List<TaxesStatistics> result = transformationService.transformToTaxesStatistics(views, yearsToQuery);

        // then
        assertThat(result).hasSize(2);

        // Check 2023 has data
        TaxesStatistics stats2023 = result.stream()
                .filter(s -> s.year() == 2023)
                .findFirst()
                .orElseThrow();
        assertThat(getTotalCount(stats2023, SubmissionType.ONLINE, 1)).isEqualTo(5);
        assertThat(getTotalRevenue(stats2023, SubmissionType.ONLINE, 1)).isEqualTo(100.0);

        // Check 2024 has zero values
        TaxesStatistics stats2024 = result.stream()
                .filter(s -> s.year() == 2024)
                .findFirst()
                .orElseThrow();
        assertThat(getTotalCount(stats2024, SubmissionType.ONLINE, 1)).isEqualTo(0);
        assertThat(getTotalRevenue(stats2024, SubmissionType.ONLINE, 1)).isEqualTo(0.0);
        assertThat(getTotalCount(stats2024, SubmissionType.ANALOG, 1)).isEqualTo(0);
        assertThat(getTotalRevenue(stats2024, SubmissionType.ANALOG, 1)).isEqualTo(0.0);
    }

    @Test
    @DisplayName("Should sort results by year in descending order")
    void transformToTaxesStatistics_ShouldSortByYearDescending() {
        // given
        TaxesStatisticsView entry1 = createStatisticsView(2022, SubmissionType.ONLINE, 1, "BE", "Office1", 1, 20.0);
        TaxesStatisticsView entry2 = createStatisticsView(2024, SubmissionType.ONLINE, 1, "BE", "Office1", 3, 60.0);
        TaxesStatisticsView entry3 = createStatisticsView(2023, SubmissionType.ONLINE, 1, "BE", "Office1", 2, 40.0);

        List<TaxesStatisticsView> views = List.of(entry1, entry2, entry3);
        List<Integer> yearsToQuery = List.of(2022, 2023, 2024);

        // when
        List<TaxesStatistics> result = transformationService.transformToTaxesStatistics(views, yearsToQuery);

        // then
        assertThat(result).hasSize(3);
        assertThat(result.get(0).year()).isEqualTo(2024); // First should be highest year
        assertThat(result.get(1).year()).isEqualTo(2023);
        assertThat(result.get(2).year()).isEqualTo(2022); // Last should be lowest year
    }

    private int getTotalCount(TaxesStatistics stats, SubmissionType submissionType, int duration) {
        return stats.data().stream()
                .filter(entry -> entry.submissionType() == submissionType && entry.duration() == duration)
                .mapToInt(TaxesStatisticsDataEntry::count)
                .sum();
    }

    private double getTotalRevenue(TaxesStatistics stats, SubmissionType submissionType, int duration) {
        return stats.data().stream()
                .filter(entry -> entry.submissionType() == submissionType && entry.duration() == duration)
                .mapToDouble(TaxesStatisticsDataEntry::revenue)
                .sum();
    }

    private TaxesStatisticsView createStatisticsView(int year, SubmissionType submissionType, int duration, String federalState, String office, int count, double revenue) {
        TaxesStatisticsView view = new TaxesStatisticsView();
        view.setYear(year);
        view.setSubmissionType(submissionType);
        view.setDuration(duration);
        view.setFederalState(federalState);
        view.setOffice(office);
        view.setCount(count);
        view.setRevenue(revenue);
        return view;
    }
}
